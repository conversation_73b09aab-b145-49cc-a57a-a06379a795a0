#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
礼品打标规律分析工具
使用GPT-4.1模型分析《代销-礼品-原始》表格中的商品数据，总结礼品打标的执行策略
"""

import pandas as pd
import json
from openai import OpenAI
from datetime import datetime
import os

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

def load_excel_data(file_path):
    """
    加载Excel表格数据
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        DataFrame: 表格数据
    """
    try:
        print(f"📊 正在加载Excel文件: {file_path}")
        
        # 尝试读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"✅ 数据加载成功!")
        print(f"📈 数据行数: {len(df)}")
        print(f"📋 数据列数: {len(df.columns)}")
        print(f"🏷️ 列名: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def analyze_data_structure(df):
    """
    分析数据结构
    
    Args:
        df: DataFrame数据
        
    Returns:
        dict: 数据结构分析结果
    """
    print("\n🔍 分析数据结构...")
    
    analysis = {
        "total_rows": len(df),
        "total_columns": len(df.columns),
        "columns": list(df.columns),
        "data_types": {str(k): str(v) for k, v in df.dtypes.to_dict().items()},
        "missing_values": {str(k): int(v) for k, v in df.isnull().sum().to_dict().items()},
        "sample_data": df.head(3).to_dict('records')
    }
    
    print("📊 数据结构分析完成:")
    print(f"   总行数: {analysis['total_rows']}")
    print(f"   总列数: {analysis['total_columns']}")
    print(f"   列名: {analysis['columns']}")
    
    # 显示缺失值情况
    missing_cols = [col for col, count in analysis['missing_values'].items() if count > 0]
    if missing_cols:
        print(f"   缺失值列: {missing_cols}")
    
    return analysis

def prepare_sample_data(df, sample_size=20):
    """
    准备样本数据用于GPT分析
    
    Args:
        df: DataFrame数据
        sample_size: 样本大小
        
    Returns:
        str: 格式化的样本数据
    """
    print(f"\n📋 准备样本数据 (样本大小: {sample_size})...")
    
    # 随机采样
    if len(df) > sample_size:
        sample_df = df.sample(n=sample_size, random_state=42)
    else:
        sample_df = df.copy()
    
    # 转换为易读格式
    sample_data = []
    for idx, row in sample_df.iterrows():
        item_data = {}
        for col in df.columns:
            value = row[col]
            # 处理NaN值
            if pd.isna(value):
                value = "无"
            item_data[col] = str(value)
        sample_data.append(item_data)
    
    # 格式化为文本
    formatted_data = "样本商品数据:\n\n"
    for i, item in enumerate(sample_data, 1):
        formatted_data += f"商品 {i}:\n"
        for key, value in item.items():
            formatted_data += f"  {key}: {value}\n"
        formatted_data += "\n"
    
    return formatted_data

def analyze_gift_patterns_with_gpt(sample_data, data_structure):
    """
    使用GPT-4.1分析礼品打标规律
    
    Args:
        sample_data: 样本数据
        data_structure: 数据结构信息
        
    Returns:
        dict: 分析结果
    """
    print("🤖 正在使用GPT-4.1分析礼品打标规律...")
    
    system_prompt = """你是一个专业的电商商品分类专家和数据分析师，具有丰富的商品分析和规律总结经验。
你需要分析人工打标为礼品的商品数据，找出礼品商品的共同特征和规律，并制定可执行的礼品打标策略。

请从以下维度进行深入分析：
1. 商品类别特征
2. 价格区间特征  
3. 品牌特征
4. 商品名称/标题特征
5. 商品描述特征
6. 其他显著特征

最终输出一套完整的礼品打标执行策略。"""

    user_prompt = f"""请分析以下已被人工打标为礼品的商品数据，总结出礼品商品的规律和特征。

数据结构信息:
- 总商品数量: {data_structure['total_rows']}
- 数据字段: {', '.join(data_structure['columns'])}

{sample_data}

请按以下JSON格式输出分析结果:
{{
    "数据概览": {{
        "样本数量": "数量",
        "主要字段": ["字段列表"],
        "数据质量评估": "评估结果"
    }},
    "礼品特征分析": {{
        "商品类别规律": ["规律1", "规律2", "..."],
        "价格区间规律": ["规律1", "规律2", "..."],
        "品牌特征规律": ["规律1", "规律2", "..."],
        "商品名称规律": ["规律1", "规律2", "..."],
        "其他显著特征": ["特征1", "特征2", "..."]
    }},
    "礼品打标策略": {{
        "核心判断标准": ["标准1", "标准2", "..."],
        "必要条件": ["条件1", "条件2", "..."],
        "充分条件": ["条件1", "条件2", "..."],
        "排除条件": ["条件1", "条件2", "..."]
    }},
    "执行建议": {{
        "自动化规则": ["规则1", "规则2", "..."],
        "人工审核要点": ["要点1", "要点2", "..."],
        "特殊情况处理": ["情况1", "情况2", "..."]
    }},
    "总结": "整体分析总结和建议"
}}"""

    try:
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.3,
            max_tokens=2000
        )
        
        analysis_result = response.choices[0].message.content.strip()
        print("✅ GPT-4.1分析完成")
        
        # 尝试解析JSON
        try:
            # 清理可能的markdown格式
            clean_result = analysis_result.strip()
            if clean_result.startswith("```json"):
                clean_result = clean_result.replace("```json", "").replace("```", "").strip()
            
            analysis_data = json.loads(clean_result)
            return analysis_data
            
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析失败: {e}")
            print("返回原始文本结果")
            return {"原始分析结果": analysis_result}
        
    except Exception as e:
        print(f"❌ GPT-4.1分析失败: {e}")
        return None

def save_analysis_results(analysis_result, data_structure):
    """
    保存分析结果
    
    Args:
        analysis_result: 分析结果
        data_structure: 数据结构信息
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存完整分析结果
    result_file = f"礼品打标分析结果_{timestamp}.json"
    
    complete_result = {
        "分析时间": timestamp,
        "数据结构": data_structure,
        "GPT分析结果": analysis_result
    }
    
    try:
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(complete_result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 分析结果已保存: {result_file}")
        
        # 同时保存可读性更好的文本版本
        text_file = f"礼品打标策略_{timestamp}.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("礼品打标规律分析报告\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"分析时间: {timestamp}\n")
            f.write(f"数据来源: 代销-礼品-原始.xlsx\n")
            f.write(f"数据规模: {data_structure['total_rows']} 条商品记录\n\n")
            
            if isinstance(analysis_result, dict):
                for key, value in analysis_result.items():
                    f.write(f"{key}:\n")
                    if isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            f.write(f"  {sub_key}: {sub_value}\n")
                    elif isinstance(value, list):
                        for item in value:
                            f.write(f"  - {item}\n")
                    else:
                        f.write(f"  {value}\n")
                    f.write("\n")
            else:
                f.write(str(analysis_result))
        
        print(f"📄 可读版本已保存: {text_file}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("🎁 礼品打标规律分析工具")
    print("=" * 80)
    print("基于GPT-4.1模型分析《代销-礼品-原始》表格数据")
    print("=" * 80)
    
    # 1. 加载数据
    excel_file = "代销-礼品-原始.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return
    
    df = load_excel_data(excel_file)
    if df is None:
        return
    
    # 2. 分析数据结构
    data_structure = analyze_data_structure(df)
    
    # 3. 准备样本数据
    sample_data = prepare_sample_data(df, sample_size=15)
    
    # 4. GPT分析
    analysis_result = analyze_gift_patterns_with_gpt(sample_data, data_structure)
    
    if analysis_result is None:
        print("❌ 分析失败，程序退出")
        return
    
    # 5. 显示结果
    print("\n" + "=" * 80)
    print("📊 GPT-4.1分析结果")
    print("=" * 80)
    
    if isinstance(analysis_result, dict):
        for key, value in analysis_result.items():
            print(f"\n🔍 {key}:")
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    print(f"   {sub_key}: {sub_value}")
            elif isinstance(value, list):
                for item in value:
                    print(f"   • {item}")
            else:
                print(f"   {value}")
    else:
        print(analysis_result)
    
    # 6. 保存结果
    save_analysis_results(analysis_result, data_structure)
    
    print("\n" + "=" * 80)
    print("✅ 礼品打标规律分析完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()
