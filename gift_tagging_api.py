#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
礼品打标API接口
提供简洁的API接口用于新商品礼品属性判断
"""

import json
from openai import OpenAI
from typing import Dict, Optional
import time

class GiftTaggingAPI:
    """礼品打标API类"""
    
    def __init__(self, api_key: str = None, base_url: str = None):
        """初始化API"""
        self.API_KEY = api_key or "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        self.BASE_URL = base_url or "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        self.client = OpenAI(api_key=self.API_KEY, base_url=self.BASE_URL)
    
    def judge_gift_attribute(self, product_info: Dict) -> Dict:
        """
        判断单个商品的礼品属性
        
        Args:
            product_info: 商品信息字典，包含以下字段：
                - sku: 商品SKU (必需)
                - 产品名称: 商品名称 (必需)
                - 品牌: 品牌信息 (必需)
                - 一级类目: 一级类目 (可选)
                - 二级类目: 二级类目 (可选)
                - 三级类目: 三级类目 (可选)
                - 四级类目: 四级类目 (可选)
                - 价格: 商品价格 (可选)
                - 商品描述: 商品描述 (可选)
        
        Returns:
            Dict: 判断结果，包含以下字段：
                - success: 是否成功 (bool)
                - gift_tag: 礼品标签 ("是"/"否"/"疑似")
                - confidence: 置信度 ("高"/"中"/"低")
                - score: 判断得分 (1-10)
                - features: 符合的特征列表
                - keywords: 匹配的关键词列表
                - reasons: 不符合的原因列表
                - suggestion: 打标建议
                - analysis: 详细分析
                - error: 错误信息 (如果失败)
        """
        
        # 验证必需字段
        required_fields = ['sku', '产品名称', '品牌']
        for field in required_fields:
            if field not in product_info or not product_info[field]:
                return {
                    "success": False,
                    "error": f"缺少必需字段: {field}"
                }
        
        # 构建系统提示词
        system_prompt = self._build_system_prompt()
        
        # 构建用户提示词
        user_prompt = self._build_user_prompt(product_info)
        
        try:
            # 调用GPT-4.1
            response = self.client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # 解析结果
            parsed_result = self._parse_gpt_result(result_text)
            
            if parsed_result:
                return {
                    "success": True,
                    "gift_tag": parsed_result.get('礼品判断', '未知'),
                    "confidence": parsed_result.get('置信度', '低'),
                    "score": parsed_result.get('判断得分', 0),
                    "features": parsed_result.get('符合特征', []),
                    "keywords": parsed_result.get('关键词匹配', []),
                    "reasons": parsed_result.get('不符合原因', []),
                    "suggestion": parsed_result.get('建议', ''),
                    "analysis": parsed_result.get('详细分析', ''),
                    "raw_response": result_text
                }
            else:
                return {
                    "success": False,
                    "error": "GPT结果解析失败",
                    "raw_response": result_text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"API调用失败: {str(e)}"
            }
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一个专业的电商商品分类专家，专门负责判断商品是否具备礼品属性。

基于对4681条人工打标礼品商品的深度分析，礼品商品具有以下特征规律：

【商品类别规律】
- 美食酒水：茶叶、乳品、零食、调味品、酒水、饮料、坚果
- 小家电：压力锅、电饭煲、豆浆机、榨汁机、咖啡机
- 日用百货：纸巾、洗护用品、收纳用品、清洁用品
- 个护美妆：洗发水、护肤品、化妆品、香水
- 服饰配件：墨镜、T恤、围巾、手表、包包
- 健康食品：保健品、营养品、有机食品

【关键词权重分析】
- 强礼品关键词(权重高)：礼盒、送礼、定制、节日、礼品、礼物
- 包装关键词(权重高)：组合装、系列、套装、多件装、礼盒装、定制款
- 场景关键词(权重中)：户外、家庭、办公、节日、商务、聚会
- 属性关键词(权重中)：无添加、氨基酸、健康、营养、品质、精选

【核心判断标准】
1. 商品具备实用性、分享性或纪念性
2. 适合在节日、商务、家庭等场景中作为礼品赠送
3. 包装规格符合送礼需求，具备体面感
4. 品牌调性偏向健康、品质、实用、时尚

【排除条件】
- 散装、裸装、基础款等低端包装商品
- 明显为个人私用、无赠送场景的商品
- 价格极低且无品牌属性的普通日用品
- 工业用品、原材料、设备配件等

请基于以上规律进行专业判断，重点关注商品名称、品牌、类目和包装特征。"""

    def _build_user_prompt(self, product_info: Dict) -> str:
        """构建用户提示词"""
        return f"""请判断以下商品是否具备礼品属性：

商品信息：
- SKU: {product_info.get('sku', '未提供')}
- 产品名称: {product_info.get('产品名称', '未提供')}
- 品牌: {product_info.get('品牌', '未提供')}
- 一级类目: {product_info.get('一级类目', '未提供')}
- 二级类目: {product_info.get('二级类目', '未提供')}
- 三级类目: {product_info.get('三级类目', '未提供')}
- 四级类目: {product_info.get('四级类目', '未提供')}
- 价格: {product_info.get('价格', '未提供')}
- 商品描述: {product_info.get('商品描述', '未提供')}

请严格按照以下JSON格式输出判断结果：
{{
    "礼品判断": "是/否/疑似",
    "置信度": "高/中/低",
    "判断得分": 数字(1-10),
    "符合特征": ["特征1", "特征2"],
    "关键词匹配": ["关键词1", "关键词2"],
    "不符合原因": ["原因1", "原因2"],
    "建议": "具体的打标建议",
    "详细分析": "详细的判断逻辑说明"
}}"""

    def _parse_gpt_result(self, result_text: str) -> Optional[Dict]:
        """解析GPT返回结果"""
        try:
            # 清理markdown格式
            clean_result = result_text.strip()
            if clean_result.startswith("```json"):
                clean_result = clean_result.replace("```json", "").replace("```", "").strip()
            elif clean_result.startswith("```"):
                clean_result = clean_result.replace("```", "").strip()
            
            # 解析JSON
            return json.loads(clean_result)
            
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试提取关键信息
            try:
                # 简单的文本解析逻辑
                lines = result_text.split('\n')
                result = {}
                
                for line in lines:
                    if '礼品判断' in line:
                        if '是' in line and '否' not in line:
                            result['礼品判断'] = '是'
                        elif '疑似' in line:
                            result['礼品判断'] = '疑似'
                        else:
                            result['礼品判断'] = '否'
                    elif '置信度' in line:
                        if '高' in line:
                            result['置信度'] = '高'
                        elif '中' in line:
                            result['置信度'] = '中'
                        else:
                            result['置信度'] = '低'
                
                if result:
                    result.setdefault('判断得分', 5)
                    result.setdefault('符合特征', [])
                    result.setdefault('关键词匹配', [])
                    result.setdefault('不符合原因', [])
                    result.setdefault('建议', '需要人工复核')
                    result.setdefault('详细分析', result_text)
                    return result
                    
            except Exception:
                pass
            
            return None

# 便捷函数
def judge_gift(product_info: Dict, api_key: str = None, base_url: str = None) -> Dict:
    """
    便捷的礼品判断函数
    
    Args:
        product_info: 商品信息
        api_key: API密钥 (可选)
        base_url: API基础URL (可选)
    
    Returns:
        Dict: 判断结果
    """
    api = GiftTaggingAPI(api_key, base_url)
    return api.judge_gift_attribute(product_info)

# 示例使用
def example_usage():
    """使用示例"""
    
    # 示例商品1: 明显的礼品
    product1 = {
        "sku": "GIFT001",
        "产品名称": "茶叶礼盒装 铁观音高档礼品茶 节日送礼佳品",
        "品牌": "八马茶业",
        "一级类目": "食品饮料",
        "二级类目": "茶叶",
        "三级类目": "乌龙茶",
        "价格": "168",
        "商品描述": "精选优质铁观音，礼盒包装，适合节日送礼"
    }
    
    # 示例商品2: 普通商品
    product2 = {
        "sku": "NORMAL001", 
        "产品名称": "普通毛巾 纯棉吸水",
        "品牌": "无名品牌",
        "一级类目": "居家日用",
        "二级类目": "毛巾",
        "价格": "15"
    }
    
    print("=" * 60)
    print("礼品打标API使用示例")
    print("=" * 60)
    
    # 测试商品1
    print(f"\n🧪 测试商品1: {product1['产品名称'][:20]}...")
    result1 = judge_gift(product1)
    
    if result1['success']:
        print(f"   结果: {result1['gift_tag']}")
        print(f"   置信度: {result1['confidence']}")
        print(f"   得分: {result1['score']}")
        print(f"   建议: {result1['suggestion']}")
    else:
        print(f"   错误: {result1['error']}")
    
    # 测试商品2
    print(f"\n🧪 测试商品2: {product2['产品名称'][:20]}...")
    result2 = judge_gift(product2)
    
    if result2['success']:
        print(f"   结果: {result2['gift_tag']}")
        print(f"   置信度: {result2['confidence']}")
        print(f"   得分: {result2['score']}")
        print(f"   建议: {result2['suggestion']}")
    else:
        print(f"   错误: {result2['error']}")
    
    print("\n" + "=" * 60)
    print("✅ 示例测试完成")
    print("=" * 60)

if __name__ == "__main__":
    example_usage()
