#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
礼品打标策略执行工具
基于GPT-4.1分析结果实现的自动化礼品打标系统
"""

import pandas as pd
import re
from typing import List, Dict, Tuple
import json

class GiftTaggingStrategy:
    """礼品打标策略类"""
    
    def __init__(self):
        """初始化礼品打标策略"""
        
        # 基于GPT-4.1分析结果的关键词规则
        self.gift_keywords = {
            "强礼品关键词": ["礼盒", "送礼", "定制", "节日", "礼品", "礼物"],
            "包装关键词": ["组合装", "系列", "套装", "多件装", "礼盒装", "定制款"],
            "规格关键词": ["收纳", "便携", "加厚", "折叠", "袋装", "箱装"],
            "场景关键词": ["户外", "家庭", "办公", "节日", "商务", "聚会"],
            "属性关键词": ["无添加", "氨基酸", "健康", "营养", "品质", "精选"]
        }
        
        # 主流礼品品类
        self.gift_categories = {
            "美食酒水": ["茶叶", "乳品", "零食", "调味品", "酒水", "饮料", "坚果"],
            "小家电": ["压力锅", "电饭煲", "豆浆机", "榨汁机", "咖啡机"],
            "日用百货": ["纸巾", "洗护", "收纳", "清洁"],
            "个护美妆": ["洗发水", "护肤", "化妆品", "香水"],
            "服饰配件": ["墨镜", "T恤", "围巾", "手表", "包包"],
            "健康食品": ["保健品", "营养品", "有机食品"]
        }
        
        # 知名品牌列表（基于分析结果）
        self.known_brands = [
            "美的", "李宁", "农夫山泉", "三得利", "海天", "南方黑芝麻",
            "趣游帮", "雪白仁", "格力", "海尔", "小米", "华为",
            "蒙牛", "伊利", "康师傅", "统一", "可口可乐", "百事"
        ]
        
        # 排除关键词
        self.exclude_keywords = [
            "散装", "裸装", "单个", "基础款", "普通装", "简装"
        ]
    
    def extract_keywords_from_text(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        if pd.isna(text) or not isinstance(text, str):
            return []
        
        text = text.lower()
        found_keywords = []
        
        # 检查所有类型的关键词
        for category, keywords in self.gift_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    found_keywords.append(f"{category}:{keyword}")
        
        return found_keywords
    
    def check_brand_reputation(self, brand: str) -> bool:
        """检查品牌知名度"""
        if pd.isna(brand) or not isinstance(brand, str):
            return False
        
        return brand in self.known_brands
    
    def check_gift_category(self, categories: List[str]) -> Tuple[bool, str]:
        """检查是否为礼品类目"""
        category_text = " ".join([str(cat) for cat in categories if pd.notna(cat)]).lower()
        
        for gift_type, keywords in self.gift_categories.items():
            for keyword in keywords:
                if keyword in category_text:
                    return True, f"{gift_type}:{keyword}"
        
        return False, ""
    
    def check_exclude_conditions(self, product_name: str) -> bool:
        """检查排除条件"""
        if pd.isna(product_name) or not isinstance(product_name, str):
            return False
        
        product_name = product_name.lower()
        
        for exclude_word in self.exclude_keywords:
            if exclude_word in product_name:
                return True
        
        return False
    
    def calculate_gift_score(self, row: pd.Series) -> Dict:
        """计算礼品得分"""
        score = 0
        reasons = []
        
        # 1. 检查产品名称中的关键词 (权重最高)
        name_keywords = self.extract_keywords_from_text(row.get('产品名称', ''))
        if name_keywords:
            score += len(name_keywords) * 3
            reasons.extend([f"产品名称包含: {kw}" for kw in name_keywords])
        
        # 2. 检查品牌知名度
        if self.check_brand_reputation(row.get('品牌', '')):
            score += 2
            reasons.append(f"知名品牌: {row.get('品牌', '')}")
        
        # 3. 检查类目匹配
        categories = [
            row.get('一级类目', ''),
            row.get('二级类目', ''),
            row.get('三级类目', ''),
            row.get('四级类目', '')
        ]
        is_gift_category, category_match = self.check_gift_category(categories)
        if is_gift_category:
            score += 2
            reasons.append(f"礼品类目: {category_match}")
        
        # 4. 检查排除条件
        if self.check_exclude_conditions(row.get('产品名称', '')):
            score -= 5
            reasons.append("包含排除关键词")
        
        return {
            "score": score,
            "reasons": reasons
        }
    
    def tag_single_product(self, row: pd.Series) -> Dict:
        """为单个商品打标"""
        result = self.calculate_gift_score(row)
        
        # 打标逻辑
        if result["score"] >= 5:
            tag = "礼品"
            confidence = "高"
        elif result["score"] >= 3:
            tag = "礼品"
            confidence = "中"
        elif result["score"] >= 1:
            tag = "疑似礼品"
            confidence = "低"
        else:
            tag = "非礼品"
            confidence = "高"
        
        return {
            "sku": row.get('sku', ''),
            "产品名称": row.get('产品名称', ''),
            "品牌": row.get('品牌', ''),
            "礼品标签": tag,
            "置信度": confidence,
            "得分": result["score"],
            "判断依据": "; ".join(result["reasons"])
        }
    
    def batch_tag_products(self, df: pd.DataFrame) -> pd.DataFrame:
        """批量为商品打标"""
        print(f"🏷️ 开始批量打标，共 {len(df)} 个商品...")
        
        results = []
        for idx, row in df.iterrows():
            if idx % 500 == 0:
                print(f"   已处理: {idx}/{len(df)}")
            
            result = self.tag_single_product(row)
            results.append(result)
        
        result_df = pd.DataFrame(results)
        
        # 统计结果
        tag_counts = result_df['礼品标签'].value_counts()
        print(f"\n📊 打标结果统计:")
        for tag, count in tag_counts.items():
            percentage = (count / len(result_df)) * 100
            print(f"   {tag}: {count} 个 ({percentage:.1f}%)")
        
        return result_df

def main():
    """主函数"""
    print("=" * 80)
    print("🎁 礼品打标策略执行工具")
    print("=" * 80)
    print("基于GPT-4.1分析结果的自动化礼品打标系统")
    print("=" * 80)
    
    # 1. 加载数据
    excel_file = "代销-礼品-原始.xlsx"
    
    try:
        print(f"📊 正在加载数据: {excel_file}")
        df = pd.read_excel(excel_file)
        print(f"✅ 数据加载成功，共 {len(df)} 条记录")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 2. 初始化打标策略
    strategy = GiftTaggingStrategy()
    
    # 3. 执行批量打标
    result_df = strategy.batch_tag_products(df)
    
    # 4. 保存结果
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    output_file = f"礼品打标结果_{timestamp}.xlsx"
    try:
        result_df.to_excel(output_file, index=False)
        print(f"💾 打标结果已保存: {output_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")
    
    # 5. 生成策略报告
    report_file = f"礼品打标策略报告_{timestamp}.txt"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("礼品打标策略执行报告\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"执行时间: {timestamp}\n")
            f.write(f"数据来源: {excel_file}\n")
            f.write(f"处理商品数量: {len(df)}\n\n")
            
            f.write("打标规则说明:\n")
            f.write("1. 强礼品关键词 (权重3): 礼盒、送礼、定制、节日等\n")
            f.write("2. 知名品牌 (权重2): 美的、李宁、农夫山泉等\n")
            f.write("3. 礼品类目 (权重2): 茶叶、零食、小家电等\n")
            f.write("4. 排除条件 (权重-5): 散装、裸装、基础款等\n\n")
            
            f.write("打标阈值:\n")
            f.write("- 得分 >= 5: 高置信度礼品\n")
            f.write("- 得分 >= 3: 中置信度礼品\n")
            f.write("- 得分 >= 1: 疑似礼品\n")
            f.write("- 得分 < 1: 非礼品\n\n")
            
            tag_counts = result_df['礼品标签'].value_counts()
            f.write("打标结果统计:\n")
            for tag, count in tag_counts.items():
                percentage = (count / len(result_df)) * 100
                f.write(f"- {tag}: {count} 个 ({percentage:.1f}%)\n")
        
        print(f"📄 策略报告已保存: {report_file}")
        
    except Exception as e:
        print(f"❌ 报告保存失败: {e}")
    
    print("\n" + "=" * 80)
    print("✅ 礼品打标策略执行完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()
