#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于GPT-4.1的新商品礼品打标判断系统
整合之前分析的策略规律，使用大模型进行智能判断
"""

import pandas as pd
import json
from openai import OpenAI
from datetime import datetime
from typing import Dict, List, Optional
import time

class GPTGiftTaggingSystem:
    """基于GPT-4.1的礼品打标系统"""
    
    def __init__(self):
        """初始化GPT客户端和策略规则"""
        
        # API配置
        self.API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
        self.BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
        self.client = OpenAI(api_key=self.API_KEY, base_url=self.BASE_URL)
        
        # 基于之前分析总结的策略规则
        self.strategy_rules = self._load_strategy_rules()
    
    def _load_strategy_rules(self) -> Dict:
        """加载策略规则"""
        return {
            "礼品特征规律": {
                "商品类别": [
                    "美食酒水：茶叶、乳品、零食、调味品、酒水、饮料、坚果",
                    "小家电：压力锅、电饭煲、豆浆机、榨汁机、咖啡机",
                    "日用百货：纸巾、洗护用品、收纳用品、清洁用品",
                    "个护美妆：洗发水、护肤品、化妆品、香水",
                    "服饰配件：墨镜、T恤、围巾、手表、包包",
                    "健康食品：保健品、营养品、有机食品"
                ],
                "价格特征": "主流价格区间20-500元，强调实用性、性价比和体面感",
                "品牌特征": "知名品牌或品质可靠品牌，调性偏向健康、品质、实用、时尚",
                "名称特征": "常含组合、礼盒、定制、系列、收纳、便携、加厚、多件装等字样",
                "包装特征": "适合赠送的包装规格，如组合装、礼盒装、定制款、便携装"
            },
            "核心判断标准": [
                "商品具备较强的实用性、分享性或纪念性",
                "适合在节日、商务、家庭等场景中作为礼品赠送",
                "包装、规格、品牌调性符合送礼需求",
                "具备一定体面感或新颖性"
            ],
            "关键词指标": {
                "强礼品关键词": ["礼盒", "送礼", "定制", "节日", "礼品", "礼物"],
                "包装关键词": ["组合装", "系列", "套装", "多件装", "礼盒装", "定制款"],
                "场景关键词": ["户外", "家庭", "办公", "节日", "商务", "聚会"],
                "属性关键词": ["无添加", "氨基酸", "健康", "营养", "品质", "精选"]
            },
            "排除条件": [
                "单价极低且无组合、无包装设计、无品牌属性的普通日用品",
                "明显为个人私用、无赠送场景的商品",
                "基础消耗品、低价散装商品",
                "商品描述中无任何送礼相关属性"
            ]
        }
    
    def create_system_prompt(self) -> str:
        """创建系统提示词"""
        return f"""你是一个专业的电商商品分类专家，专门负责判断商品是否具备礼品属性。

基于对4681条人工打标礼品商品的深度分析，礼品商品具有以下特征规律：

【商品类别规律】
{chr(10).join(self.strategy_rules['礼品特征规律']['商品类别'])}

【价格特征】
{self.strategy_rules['礼品特征规律']['价格特征']}

【品牌特征】
{self.strategy_rules['礼品特征规律']['品牌特征']}

【名称特征】
{self.strategy_rules['礼品特征规律']['名称特征']}

【包装特征】
{self.strategy_rules['礼品特征规律']['包装特征']}

【核心判断标准】
{chr(10).join([f"- {std}" for std in self.strategy_rules['核心判断标准']])}

【关键词指标】
- 强礼品关键词：{', '.join(self.strategy_rules['关键词指标']['强礼品关键词'])}
- 包装关键词：{', '.join(self.strategy_rules['关键词指标']['包装关键词'])}
- 场景关键词：{', '.join(self.strategy_rules['关键词指标']['场景关键词'])}
- 属性关键词：{', '.join(self.strategy_rules['关键词指标']['属性关键词'])}

【排除条件】
{chr(10).join([f"- {cond}" for cond in self.strategy_rules['排除条件']])}

请基于以上规律和标准，对商品进行礼品属性判断。"""

    def create_user_prompt(self, product_info: Dict) -> str:
        """创建用户提示词"""
        return f"""请判断以下商品是否具备礼品属性：

商品信息：
- SKU: {product_info.get('sku', '未提供')}
- 产品名称: {product_info.get('产品名称', '未提供')}
- 一级类目: {product_info.get('一级类目', '未提供')}
- 二级类目: {product_info.get('二级类目', '未提供')}
- 三级类目: {product_info.get('三级类目', '未提供')}
- 四级类目: {product_info.get('四级类目', '未提供')}
- 品牌: {product_info.get('品牌', '未提供')}
- 价格: {product_info.get('价格', '未提供')}
- 商品描述: {product_info.get('商品描述', '未提供')}

请按以下JSON格式输出判断结果：
{{
    "礼品判断": "是/否/疑似",
    "置信度": "高/中/低",
    "判断得分": "1-10分",
    "符合特征": ["列出符合的礼品特征"],
    "关键词匹配": ["匹配到的关键词"],
    "不符合原因": ["如果不是礼品，列出原因"],
    "建议": "具体的打标建议",
    "详细分析": "详细的判断逻辑说明"
}}"""

    def judge_single_product(self, product_info: Dict) -> Optional[Dict]:
        """判断单个商品的礼品属性"""
        
        system_prompt = self.create_system_prompt()
        user_prompt = self.create_user_prompt(product_info)
        
        try:
            print(f"🤖 正在使用GPT-4.1判断商品: {product_info.get('产品名称', 'Unknown')[:30]}...")
            
            response = self.client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,  # 低温度确保结果稳定
                max_tokens=1000
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # 尝试解析JSON
            try:
                # 清理可能的markdown格式
                clean_result = result_text.strip()
                if clean_result.startswith("```json"):
                    clean_result = clean_result.replace("```json", "").replace("```", "").strip()
                
                result_data = json.loads(clean_result)
                
                # 添加原始商品信息
                result_data["原始商品信息"] = product_info
                result_data["判断时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                return result_data
                
            except json.JSONDecodeError as e:
                print(f"⚠️ JSON解析失败: {e}")
                return {
                    "礼品判断": "解析失败",
                    "置信度": "低",
                    "判断得分": 0,
                    "原始结果": result_text,
                    "原始商品信息": product_info,
                    "判断时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
        
        except Exception as e:
            print(f"❌ GPT-4.1判断失败: {e}")
            return None
    
    def batch_judge_products(self, products: List[Dict], batch_size: int = 10) -> List[Dict]:
        """批量判断商品礼品属性"""
        
        print(f"🏷️ 开始批量判断，共 {len(products)} 个商品...")
        print(f"📦 批次大小: {batch_size}")
        
        results = []
        
        for i in range(0, len(products), batch_size):
            batch = products[i:i+batch_size]
            print(f"\n📋 处理批次 {i//batch_size + 1}/{(len(products)-1)//batch_size + 1}")
            
            batch_results = []
            for j, product in enumerate(batch):
                print(f"   处理商品 {i+j+1}/{len(products)}")
                
                result = self.judge_single_product(product)
                if result:
                    batch_results.append(result)
                    
                    # 显示判断结果
                    gift_status = result.get('礼品判断', '未知')
                    confidence = result.get('置信度', '未知')
                    score = result.get('判断得分', 0)
                    print(f"      结果: {gift_status} (置信度: {confidence}, 得分: {score})")
                else:
                    print(f"      结果: 判断失败")
                
                # API调用间隔，避免频率限制
                time.sleep(0.5)
            
            results.extend(batch_results)
            
            # 批次间稍长间隔
            if i + batch_size < len(products):
                print(f"   批次完成，等待2秒...")
                time.sleep(2)
        
        return results
    
    def analyze_batch_results(self, results: List[Dict]) -> Dict:
        """分析批量判断结果"""
        
        if not results:
            return {"error": "无有效结果"}
        
        # 统计各类结果
        gift_count = len([r for r in results if r.get('礼品判断') == '是'])
        maybe_gift_count = len([r for r in results if r.get('礼品判断') == '疑似'])
        not_gift_count = len([r for r in results if r.get('礼品判断') == '否'])
        failed_count = len([r for r in results if r.get('礼品判断') in ['解析失败', '未知']])
        
        # 置信度统计
        high_confidence = len([r for r in results if r.get('置信度') == '高'])
        medium_confidence = len([r for r in results if r.get('置信度') == '中'])
        low_confidence = len([r for r in results if r.get('置信度') == '低'])
        
        # 得分统计
        scores = [r.get('判断得分', 0) for r in results if isinstance(r.get('判断得分'), (int, float))]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        return {
            "总商品数": len(results),
            "判断结果统计": {
                "礼品": gift_count,
                "疑似礼品": maybe_gift_count,
                "非礼品": not_gift_count,
                "判断失败": failed_count
            },
            "置信度统计": {
                "高置信度": high_confidence,
                "中置信度": medium_confidence,
                "低置信度": low_confidence
            },
            "平均得分": round(avg_score, 2),
            "礼品识别率": f"{((gift_count + maybe_gift_count) / len(results) * 100):.1f}%"
        }
    
    def save_results(self, results: List[Dict], analysis: Dict) -> str:
        """保存判断结果"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果到Excel
        if results:
            # 准备DataFrame数据
            df_data = []
            for result in results:
                original_info = result.get("原始商品信息", {})
                row = {
                    "SKU": original_info.get('sku', ''),
                    "产品名称": original_info.get('产品名称', ''),
                    "品牌": original_info.get('品牌', ''),
                    "一级类目": original_info.get('一级类目', ''),
                    "二级类目": original_info.get('二级类目', ''),
                    "三级类目": original_info.get('三级类目', ''),
                    "价格": original_info.get('价格', ''),
                    "礼品判断": result.get('礼品判断', ''),
                    "置信度": result.get('置信度', ''),
                    "判断得分": result.get('判断得分', ''),
                    "符合特征": '; '.join(result.get('符合特征', [])),
                    "关键词匹配": '; '.join(result.get('关键词匹配', [])),
                    "不符合原因": '; '.join(result.get('不符合原因', [])),
                    "建议": result.get('建议', ''),
                    "详细分析": result.get('详细分析', ''),
                    "判断时间": result.get('判断时间', '')
                }
                df_data.append(row)
            
            df = pd.DataFrame(df_data)
            excel_file = f"GPT礼品打标结果_{timestamp}.xlsx"
            df.to_excel(excel_file, index=False)
            print(f"💾 详细结果已保存: {excel_file}")
        
        # 保存分析报告
        report_file = f"GPT礼品打标分析报告_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("基于GPT-4.1的礼品打标分析报告\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"分析时间: {timestamp}\n")
            f.write(f"使用模型: GPT-4.1\n\n")
            
            for key, value in analysis.items():
                f.write(f"{key}: ")
                if isinstance(value, dict):
                    f.write("\n")
                    for sub_key, sub_value in value.items():
                        f.write(f"  {sub_key}: {sub_value}\n")
                else:
                    f.write(f"{value}\n")
                f.write("\n")
        
        print(f"📄 分析报告已保存: {report_file}")
        
        return excel_file

def main():
    """主函数 - 演示如何使用系统"""
    print("=" * 80)
    print("🎁 基于GPT-4.1的新商品礼品打标判断系统")
    print("=" * 80)
    print("整合之前策略分析结果，使用大模型进行智能判断")
    print("=" * 80)
    
    # 初始化系统
    system = GPTGiftTaggingSystem()
    
    # 示例商品数据（实际使用时替换为真实数据）
    sample_products = [
        {
            "sku": "TEST001",
            "产品名称": "美的电压力锅家用智能多功能高压锅5L大容量",
            "一级类目": "家用电器",
            "二级类目": "厨房电器",
            "三级类目": "电压力锅",
            "四级类目": "",
            "品牌": "美的",
            "价格": "299",
            "商品描述": "智能预约，多种烹饪模式，适合家庭使用"
        },
        {
            "sku": "TEST002", 
            "产品名称": "农夫山泉天然水550ml*24瓶整箱装",
            "一级类目": "食品饮料",
            "二级类目": "饮用水",
            "三级类目": "天然水",
            "四级类目": "",
            "品牌": "农夫山泉",
            "价格": "45",
            "商品描述": "天然弱碱性水，整箱装便于储存和分享"
        }
    ]
    
    print(f"\n🧪 使用示例数据进行测试 ({len(sample_products)} 个商品)")
    
    # 执行判断
    results = system.batch_judge_products(sample_products, batch_size=5)
    
    # 分析结果
    analysis = system.analyze_batch_results(results)
    
    # 显示分析结果
    print("\n" + "=" * 80)
    print("📊 判断结果分析")
    print("=" * 80)
    for key, value in analysis.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")
    
    # 保存结果
    if results:
        system.save_results(results, analysis)
    
    print("\n" + "=" * 80)
    print("✅ GPT-4.1礼品打标判断完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()
