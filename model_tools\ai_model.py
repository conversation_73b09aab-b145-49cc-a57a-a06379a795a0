from openai import OpenAI

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

system_prompt = (
    "你是一个专业的电商商品分类专家，具有丰富的商品判断经验。请仔细分析商品信息，特别注意商品的实际销售模式和市场定位。"
)
user_prompt = (
    "请根据以下商品信息，判断该商品的是否具备礼品属性。"
)

response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            temperature=0.7,
        )

response_text = response.choices[0].message.content
print("AI:\n", response_text)