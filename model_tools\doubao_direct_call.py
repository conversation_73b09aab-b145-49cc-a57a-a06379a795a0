#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包模型直接调用工具
基于您提供的curl命令逻辑，直接调用豆包模型进行图像优化
"""

import requests
import json
import time
from datetime import datetime


def call_doubao_api(
    image_url: str,
    prompt: str,
    api_key: str = "0b2f220d-bba5-4650-8725-64757bbc1113",
    model: str = "doubao-seededit-3-0-i2i-250628",
    seed: int = 21,
    guidance_scale: float = 5.5
):
    """
    直接调用豆包API
    
    Args:
        image_url: 图像URL
        prompt: 提示词
        api_key: API密钥
        model: 模型名称
        seed: 随机种子
        guidance_scale: 引导强度
    """
    
    url = "https://ark.cn-beijing.volces.com/api/v3/images/generations"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    data = {
        "model": model,
        "prompt": prompt,
        "image": image_url,
        "response_format": "url",
        "size": "adaptive",
        "seed": seed,
        "guidance_scale": guidance_scale,
        "watermark": True
    }
    
    print("🚀 正在调用豆包API...")
    print(f"📝 提示词: {prompt}")
    print(f"🖼️ 图像URL: {image_url}")
    print(f"🎯 模型: {model}")
    print(f"🎲 种子: {seed}")
    print(f"📊 引导强度: {guidance_scale}")
    
    start_time = time.time()
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=120)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱️ 请求耗时: {processing_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功！")
            
            # 保存响应
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            response_file = f"doubao_response_{timestamp}.json"
            
            with open(response_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 响应已保存: {response_file}")
            
            # 打印结果
            print("\n📊 API响应结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 提取图像URL
            if 'data' in result and len(result['data']) > 0:
                optimized_url = result['data'][0].get('url')
                if optimized_url:
                    print(f"\n🎨 优化后图像URL: {optimized_url}")
                    
                    # 下载图像
                    download_image(optimized_url, f"doubao_optimized_{timestamp}.png")
            
            return result
            
        else:
            print(f"❌ API调用失败")
            print(f"状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None


def download_image(url: str, filename: str):
    """下载图像"""
    try:
        print(f"📥 正在下载图像: {filename}")
        
        response = requests.get(url, timeout=60)
        
        if response.status_code == 200:
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 图像下载成功: {filename}")
        else:
            print(f"❌ 图像下载失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 下载异常: {e}")


def generate_cat_food_fusion_prompt():
    """生成猫粮融合优化提示词"""
    return """对图像中的猫粮产品进行自然融合优化，严格要求：

🔒 绝对保护商品信息：
- 不能修改猫粮包装上的任何文字、品牌标识、营养成分标签
- 保持猫粮包装的原始颜色、图案、形状、材质完全不变
- 不能改变包装上的任何视觉元素

✨ 环境融合优化：
- 在猫粮包装底部和周围添加自然的阴影效果
- 调整环境光照，使猫粮看起来自然融入背景场景
- 优化猫粮包装边缘与背景的过渡，消除抠图或合成痕迹
- 增强整体画面的真实感和专业摄影效果

🎯 最终目标：
- 产出完全看不出人工合成痕迹的自然图像
- 保持猫粮包装上所有文字和信息清晰可读
- 达到专业产品摄影的视觉质量"""


def test_with_sample_image():
    """使用示例图像进行测试"""
    print("🧪 使用示例图像进行豆包API测试")
    print("=" * 60)
    
    # 使用豆包官方示例图像
    sample_image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/seedream_i2i.jpeg"
    
    # 生成猫粮融合提示词
    fusion_prompt = generate_cat_food_fusion_prompt()
    
    # 调用API
    result = call_doubao_api(
        image_url=sample_image_url,
        prompt=fusion_prompt,
        seed=21,
        guidance_scale=5.5
    )
    
    if result:
        print("\n🎉 测试成功完成！")
    else:
        print("\n❌ 测试失败")


def optimize_custom_image():
    """优化自定义图像"""
    print("🎨 自定义图像优化")
    print("=" * 60)
    
    # 获取用户输入
    image_url = input("请输入图像URL: ").strip()
    if not image_url:
        print("❌ 图像URL不能为空")
        return
    
    print("\n请选择提示词类型:")
    print("1. 猫粮产品融合优化（推荐）")
    print("2. 自定义提示词")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        prompt = generate_cat_food_fusion_prompt()
    elif choice == "2":
        prompt = input("请输入自定义提示词: ").strip()
        if not prompt:
            print("❌ 提示词不能为空")
            return
    else:
        print("❌ 无效选择")
        return
    
    # 获取其他参数
    try:
        seed = int(input("请输入随机种子 (默认21): ").strip() or "21")
        guidance_scale = float(input("请输入引导强度 (默认5.5): ").strip() or "5.5")
    except ValueError:
        print("❌ 参数格式错误，使用默认值")
        seed = 21
        guidance_scale = 5.5
    
    # 调用API
    result = call_doubao_api(
        image_url=image_url,
        prompt=prompt,
        seed=seed,
        guidance_scale=guidance_scale
    )
    
    if result:
        print("\n🎉 优化完成！")
    else:
        print("\n❌ 优化失败")


def main():
    """主函数"""
    print("🎨 豆包模型图像优化工具")
    print("=" * 60)
    print("基于您提供的curl命令逻辑实现")
    print("=" * 60)
    
    print("\n请选择操作:")
    print("1. 使用示例图像测试API")
    print("2. 优化自定义图像")
    print("3. 查看curl命令示例")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        test_with_sample_image()
    elif choice == "2":
        optimize_custom_image()
    elif choice == "3":
        print("\n📋 等效的curl命令示例:")
        print("""
curl -X POST https://ark.cn-beijing.volces.com/api/v3/images/generations \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer 0b2f220d-bba5-4650-8725-64757bbc1113" \\
  -d '{
    "model": "doubao-seededit-3-0-i2i-250628",
    "prompt": "对图像中的猫粮产品进行自然融合优化...",
    "image": "YOUR_IMAGE_URL_HERE",
    "response_format": "url",
    "size": "adaptive",
    "seed": 21,
    "guidance_scale": 5.5,
    "watermark": true
}'
        """)
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
