#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from openai import OpenAI

def analyze_image_realism_with_gpt4o(image_url):
    """使用GPT-4o分析图片的真实性问题并生成优化提示词"""
    
    # GPT-4o API配置
    API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
    BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    client = OpenAI(api_key=API_KEY, base_url=BASE_URL)
    
    system_prompt = (
        "你是一个专业的摄影师和图像分析专家，擅长识别合成图片中不自然的地方。"
        "请仔细分析图片，找出所有不符合真实摄影的问题，并提供专业的优化建议。"
    )
    
    user_prompt = """请仔细分析这张图片，从专业摄影师的角度指出所有不符合真实摄影的问题，并提供优化建议。

请从以下几个方面进行详细分析：

1. **光影问题**：
   - 光源方向是否一致
   - 阴影是否自然合理
   - 高光和反射是否符合物理规律
   - 整体光照是否协调

2. **色彩问题**：
   - 色温是否匹配
   - 色彩饱和度是否自然
   - 色彩过渡是否平滑
   - 整体色调是否和谐

3. **边缘和融合问题**：
   - 物体边缘是否自然
   - 是否有明显的抠图痕迹
   - 景深效果是否合理
   - 空间关系是否正确

4. **质感和细节问题**：
   - 材质表现是否真实
   - 细节层次是否丰富
   - 纹理是否自然
   - 整体质感是否统一

5. **透视和比例问题**：
   - 透视关系是否正确
   - 物体比例是否合理
   - 空间感是否自然

请用以下JSON格式回答：
{
    "problems": [
        {
            "category": "问题类别",
            "description": "具体问题描述",
            "severity": "严重程度(高/中/低)"
        }
    ],
    "optimization_prompt": "基于发现的问题生成的详细优化提示词",
    "overall_assessment": "整体评估和建议"
}"""
    
    try:
        print("🔍 正在使用GPT-4o分析图片真实性...")
        print(f"📷 分析图片: {image_url}")
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user", 
                    "content": [
                        {"type": "text", "text": user_prompt},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                }
            ],
            temperature=0.1,
            max_tokens=1500
        )
        
        analysis_result = response.choices[0].message.content.strip()
        print(f"✅ GPT-4o分析完成")
        print(f"📝 分析结果长度: {len(analysis_result)} 字符")
        
        # 尝试解析JSON
        try:
            # 清理可能的markdown格式
            clean_result = analysis_result.strip()
            if clean_result.startswith("```json"):
                clean_result = clean_result.replace("```json", "").replace("```", "").strip()
            
            analysis_data = json.loads(clean_result)
            return analysis_data
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析失败: {e}")
            print(f"原始结果: {analysis_result}")
            # 返回原始文本结果
            return {
                "problems": [{"category": "解析错误", "description": "无法解析JSON格式", "severity": "低"}],
                "optimization_prompt": analysis_result,
                "overall_assessment": "需要手动解析分析结果"
            }
        
    except Exception as e:
        print(f"❌ GPT-4o分析失败: {e}")
        return None

def optimize_image_with_doubao(image_url, optimization_prompt):
    """使用豆包API根据优化提示词改进图片"""
    
    # 豆包API配置
    doubao_url = "https://ark.cn-beijing.volces.com/api/v3/images/generations"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer e3cf3f49-f86f-4bce-b3b6-e389c96e91ee"
    }
    
    payload = {
        "model": "doubao-seededit-3-0-i2i-250628",
        "prompt": optimization_prompt,
        "image": image_url,
        "response_format": "url",
        "size": "adaptive",
        "seed": 888,  # 使用新的seed
        "guidance_scale": 8.5,  # 高引导强度确保精确优化
        "watermark": True
    }
    
    print("🎨 正在使用豆包API进行针对性优化...")
    print(f"📝 优化提示词: {optimization_prompt[:200]}...")
    
    try:
        response = requests.post(
            doubao_url,
            headers=headers,
            json=payload,
            timeout=300
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and len(result['data']) > 0:
                optimized_url = result['data'][0]['url']
                print(f"✅ 豆包针对性优化成功!")
                print(f"🔗 优化结果: {optimized_url}")
                return optimized_url
        else:
            print(f"❌ 豆包优化失败，状态码: {response.status_code}")
            print(f"错误响应: {response.text}")
        return None
        
    except Exception as e:
        print(f"❌ 豆包优化异常: {e}")
        return None

def main():
    """主函数：GPT-4o图片分析 + 豆包优化流水线"""
    
    # 输入图片URL - 高级优化版本
    image_url = "https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seededit-3-0-i2i/021752822850837a4e1a8628c9ddb40931af395d7878e374e5fc9.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYjg3ZjNlOGM0YzQyNGE1MmI2MDFiOTM3Y2IwMTY3OTE%2F20250718%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250718T071421Z&X-Tos-Expires=86400&X-Tos-Signature=f2f7e5cd1f7eda7f4015aa1af4c1ec548edcbfcb11c9cd6da6f52e8f51fa5d6b&X-Tos-SignedHeaders=host&x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF8xNg%3D%3D"
    
    print("=" * 120)
    print("🚀 启动 GPT-4o 图片真实性分析 + 豆包优化流水线")
    print("=" * 120)
    print(f"📷 分析图片: {image_url[:100]}...")
    print(f"🎯 分析目标: 找出不符合真实摄影的问题并生成优化方案")
    print()
    
    # 第一步：GPT-4o分析图片真实性问题
    print("📋 第一步：GPT-4o专业图片真实性分析")
    analysis_result = analyze_image_realism_with_gpt4o(image_url)
    
    if not analysis_result:
        print("❌ 图片分析失败，无法继续")
        return
    
    print()
    print("📊 分析结果详情:")
    print("-" * 80)
    
    # 显示发现的问题
    if 'problems' in analysis_result and analysis_result['problems']:
        print("🔍 发现的问题:")
        for i, problem in enumerate(analysis_result['problems'], 1):
            severity_emoji = {"高": "🔴", "中": "🟡", "低": "🟢"}.get(problem.get('severity', '中'), "⚪")
            print(f"  {i}. {severity_emoji} [{problem.get('category', '未知')}] {problem.get('description', '无描述')}")
    
    # 显示整体评估
    if 'overall_assessment' in analysis_result:
        print(f"\n📝 整体评估: {analysis_result['overall_assessment']}")
    
    # 显示优化提示词
    if 'optimization_prompt' in analysis_result:
        print(f"\n🎯 生成的优化提示词:")
        print(f"📝 {analysis_result['optimization_prompt']}")
    
    print()
    
    # 第二步：使用豆包进行针对性优化
    if 'optimization_prompt' in analysis_result and analysis_result['optimization_prompt']:
        print("📋 第二步：使用豆包进行针对性优化")
        optimized_url = optimize_image_with_doubao(image_url, analysis_result['optimization_prompt'])
        print()
        
        # 总结
        print("=" * 120)
        print("📊 GPT-4o分析 + 豆包优化结果总结")
        print("=" * 120)
        print(f"📷 原始图片: {image_url[:80]}...")
        
        if analysis_result.get('problems'):
            print(f"🔍 发现问题数量: {len(analysis_result['problems'])} 个")
            high_severity = len([p for p in analysis_result['problems'] if p.get('severity') == '高'])
            if high_severity > 0:
                print(f"🔴 高严重性问题: {high_severity} 个")
        
        if optimized_url:
            print(f"✅ 针对性优化成功!")
            print(f"🔗 最终优化结果: {optimized_url}")
            print(f"🎯 优化效果: 基于GPT-4o专业分析的精准优化")
        else:
            print(f"❌ 针对性优化失败")
        
        print("=" * 120)
    else:
        print("⚠️ 无法获取有效的优化提示词，跳过豆包优化步骤")

if __name__ == "__main__":
    main()
