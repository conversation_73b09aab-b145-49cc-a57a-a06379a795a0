#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT Image API 调用模块
用于调用gpt-image-1模型进行图像编辑
"""

import base64
import json
import requests
from typing import Optional, Dict, Any, Union
from pathlib import Path
import mimetypes


class GPTImageAPI:
    """GPT Image API 客户端"""
    
    def __init__(self, api_key: str, base_url: str = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"):
        """
        初始化GPT Image API客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def encode_image_to_base64(self, image_path: Union[str, Path]) -> Optional[str]:
        """
        将图像文件编码为base64字符串
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            base64编码的图像字符串，失败返回None
        """
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                print(f"❌ 图像文件不存在: {image_path}")
                return None
            
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                print(f"✅ 图像编码成功: {image_path.name}")
                return encoded_string
                
        except Exception as e:
            print(f"❌ 图像编码失败: {e}")
            return None
    
    def get_image_format(self, image_path: Union[str, Path]) -> str:
        """
        获取图像格式
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像格式（如'png', 'jpeg'等）
        """
        try:
            mime_type, _ = mimetypes.guess_type(str(image_path))
            if mime_type and mime_type.startswith('image/'):
                return mime_type.split('/')[-1].lower()
            else:
                # 默认返回png
                return 'png'
        except:
            return 'png'
    
    def call_gpt_image_edit(
        self,
        image_path: Union[str, Path],
        prompt: str,
        mask_path: Optional[Union[str, Path]] = None,
        max_tokens: int = 100,
        model: str = "gpt-image-1-edit",
        stream: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        调用GPT Image编辑API
        
        Args:
            image_path: 原始图像文件路径
            prompt: 编辑提示词
            mask_path: 遮罩图像文件路径（可选）
            max_tokens: 最大token数
            model: 模型名称
            stream: 是否流式返回
            
        Returns:
            API响应结果，失败返回None
        """
        # 编码原始图像
        image_base64 = self.encode_image_to_base64(image_path)
        if not image_base64:
            return None
        
        # 获取图像格式
        image_format = self.get_image_format(image_path)
        
        # 构建消息内容
        content = [
            {
                "type": "image",
                "content": image_base64,
                "external": {
                    "images": {
                        "format": image_format
                    }
                }
            },
            {
                "type": "text",
                "content": prompt
            }
        ]
        
        # 如果提供了遮罩图像，添加到external中
        if mask_path:
            mask_base64 = self.encode_image_to_base64(mask_path)
            if mask_base64:
                content[0]["external"]["images"]["mask"] = mask_base64
                print(f"✅ 遮罩图像已添加: {Path(mask_path).name}")
            else:
                print("⚠️ 遮罩图像编码失败，将忽略遮罩")
        
        # 构建请求数据
        request_data = {
            "maxTokens": max_tokens,
            "messages": [
                {
                    "content": content,
                    "role": "user"
                }
            ],
            "model": model,
            "stream": stream
        }
        
        return self._send_request(request_data)
    
    def _send_request(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送API请求
        
        Args:
            request_data: 请求数据
            
        Returns:
            API响应结果
        """
        try:
            print("🚀 正在调用GPT Image API...")
            print(f"📋 模型: {request_data['model']}")
            print(f"📝 提示词: {request_data['messages'][0]['content'][1]['content'][:50]}...")
            
            # 发送POST请求（增加超时时间）
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=request_data,
                timeout=180  # 增加到3分钟
            )
            
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                print("✅ API调用成功")
                return result
            else:
                print(f"❌ API调用失败，状态码: {response.status_code}")
                print(f"❌ 错误信息: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print("❌ API请求超时")
            return None
        except requests.exceptions.RequestException as e:
            print(f"❌ API请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            return None
    
    def extract_response_content(self, api_response: Dict[str, Any]) -> Optional[str]:
        """
        从API响应中提取内容
        
        Args:
            api_response: API响应数据
            
        Returns:
            响应内容文本
        """
        try:
            if "choices" in api_response and len(api_response["choices"]) > 0:
                choice = api_response["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    return choice["message"]["content"]
            
            print("❌ 无法从响应中提取内容")
            return None
            
        except Exception as e:
            print(f"❌ 提取响应内容失败: {e}")
            return None
    
    def print_response_info(self, api_response: Dict[str, Any]) -> None:
        """
        打印API响应信息
        
        Args:
            api_response: API响应数据
        """
        try:
            print("\n📊 API响应信息:")
            
            # 基本信息
            if "id" in api_response:
                print(f"🆔 请求ID: {api_response['id']}")
            
            if "model" in api_response:
                print(f"🤖 使用模型: {api_response['model']}")
            
            if "created" in api_response:
                print(f"🕒 创建时间: {api_response['created']}")
            
            # Token使用情况
            if "usage" in api_response:
                usage = api_response["usage"]
                print(f"📈 Token使用情况:")
                if "prompt_tokens" in usage:
                    print(f"   输入Token: {usage['prompt_tokens']}")
                if "completion_tokens" in usage:
                    print(f"   输出Token: {usage['completion_tokens']}")
                if "total_tokens" in usage:
                    print(f"   总Token: {usage['total_tokens']}")
            
            # 响应内容
            content = self.extract_response_content(api_response)
            if content:
                print(f"💬 响应内容: {content[:100]}...")
            
        except Exception as e:
            print(f"❌ 打印响应信息失败: {e}")


def main():
    """示例使用"""
    # 使用现有的API配置
    API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
    BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    
    # 初始化客户端
    client = GPTImageAPI(API_KEY, BASE_URL)
    
    print("=" * 80)
    print("🎨 GPT Image API 测试")
    print("=" * 80)
    
    # 示例：图像编辑
    image_path = "test_image.jpg"  # 替换为实际的图像路径
    prompt = "将这张图片的背景改为蓝色天空"
    
    # 调用API
    response = client.call_gpt_image_edit(
        image_path=image_path,
        prompt=prompt,
        max_tokens=100
    )
    
    if response:
        # 打印响应信息
        client.print_response_info(response)
        
        # 提取响应内容
        content = client.extract_response_content(response)
        if content:
            print(f"\n🎯 编辑结果: {content}")
    else:
        print("❌ API调用失败")


if __name__ == "__main__":
    main()
