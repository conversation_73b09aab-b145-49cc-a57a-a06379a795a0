# 基于GPT-4.1的新商品礼品打标判断逻辑

## 🎯 总体策略

基于对4681条人工打标礼品商品的深度分析，整合规律特征，使用GPT-4.1模型对新商品进行智能礼品属性判断。

## 📋 判断逻辑框架

### 1. 输入信息要求
```
必需字段：
- SKU: 商品唯一标识
- 产品名称: 商品完整名称
- 品牌: 商品品牌信息
- 类目信息: 一级/二级/三级/四级类目

可选字段：
- 价格: 商品价格
- 商品描述: 详细描述信息
```

### 2. 核心判断维度

#### 维度1: 商品类别匹配 (权重: 25%)
**礼品类别清单**:
- 美食酒水: 茶叶、乳品、零食、调味品、酒水、饮料、坚果
- 小家电: 压力锅、电饭煲、豆浆机、榨汁机、咖啡机
- 日用百货: 纸巾、洗护用品、收纳用品、清洁用品
- 个护美妆: 洗发水、护肤品、化妆品、香水
- 服饰配件: 墨镜、T恤、围巾、手表、包包
- 健康食品: 保健品、营养品、有机食品

**判断逻辑**:
```python
if 商品类目 in 礼品类别清单:
    类别得分 = 8-10分
elif 商品类目 in 潜在礼品类别:
    类别得分 = 5-7分
else:
    类别得分 = 0-4分
```

#### 维度2: 关键词特征分析 (权重: 30%)
**关键词分类**:
```
强礼品关键词 (权重3): 礼盒、送礼、定制、节日、礼品、礼物
包装关键词 (权重3): 组合装、系列、套装、多件装、礼盒装、定制款
场景关键词 (权重2): 户外、家庭、办公、节日、商务、聚会
属性关键词 (权重2): 无添加、氨基酸、健康、营养、品质、精选
规格关键词 (权重1): 收纳、便携、加厚、折叠、袋装、箱装
```

**计算公式**:
```python
关键词得分 = Σ(匹配关键词数量 × 对应权重)
if 关键词得分 >= 6: 特征得分 = 9-10分
elif 关键词得分 >= 3: 特征得分 = 6-8分
elif 关键词得分 >= 1: 特征得分 = 3-5分
else: 特征得分 = 0-2分
```

#### 维度3: 品牌信誉度 (权重: 20%)
**品牌分类**:
```
知名品牌: 美的、李宁、农夫山泉、三得利、海天、南方黑芝麻等
区域头部品牌: 具有一定知名度的区域性品牌
新兴品牌: 有特色包装或创新概念的新品牌
无品牌/杂牌: 品牌知名度极低或无品牌
```

**判断逻辑**:
```python
if 品牌 in 知名品牌列表:
    品牌得分 = 8-10分
elif 品牌调性符合礼品特征:
    品牌得分 = 5-7分
else:
    品牌得分 = 0-4分
```

#### 维度4: 价格合理性 (权重: 15%)
**价格区间判断**:
```
礼品主流价格区间: 20-500元
- 20-100元: 日常小礼品，得分6-8分
- 100-300元: 主流礼品价格，得分8-10分  
- 300-500元: 高端礼品，得分7-9分
- 500元以上: 需结合品类和场景，得分5-8分
- 20元以下: 除非有特殊包装，得分0-3分
```

#### 维度5: 包装适宜性 (权重: 10%)
**包装特征识别**:
```
高适宜性: 礼盒装、定制款、组合装、套装
中适宜性: 精装版、特别版、限量版
低适宜性: 普通包装但品质较好
不适宜: 散装、裸装、简装
```

### 3. 排除条件检查

**强制排除**:
- 包含关键词: 散装、裸装、基础款、普通装、简装
- 明显个人私用品: 内衣、个人卫生用品、处方药品
- 工业用品: 原材料、工具、设备配件
- 极低价无品牌商品: 价格<10元且无知名品牌

### 4. 综合评分算法

```python
综合得分 = (
    类别得分 × 0.25 + 
    关键词得分 × 0.30 + 
    品牌得分 × 0.20 + 
    价格得分 × 0.15 + 
    包装得分 × 0.10
)

# 排除条件调整
if 触发排除条件:
    综合得分 -= 3分
```

### 5. 最终判断标准

```python
if 综合得分 >= 8.0:
    判断结果 = "是"
    置信度 = "高"
elif 综合得分 >= 6.0:
    判断结果 = "是" 
    置信度 = "中"
elif 综合得分 >= 4.0:
    判断结果 = "疑似"
    置信度 = "中"
elif 综合得分 >= 2.0:
    判断结果 = "疑似"
    置信度 = "低"
else:
    判断结果 = "否"
    置信度 = "高"
```

## 🤖 GPT-4.1 Prompt 设计

### System Prompt 模板
```
你是一个专业的电商商品分类专家，专门负责判断商品是否具备礼品属性。

基于对4681条人工打标礼品商品的深度分析，请按照以下规律进行判断：

【商品类别规律】
- 美食酒水：茶叶、乳品、零食、调味品、酒水、饮料、坚果
- 小家电：压力锅、电饭煲、豆浆机、榨汁机、咖啡机
- 日用百货：纸巾、洗护用品、收纳用品、清洁用品
- 个护美妆：洗发水、护肤品、化妆品、香水
- 服饰配件：墨镜、T恤、围巾、手表、包包
- 健康食品：保健品、营养品、有机食品

【关键词权重】
- 强礼品关键词(权重3)：礼盒、送礼、定制、节日、礼品、礼物
- 包装关键词(权重3)：组合装、系列、套装、多件装、礼盒装、定制款
- 场景关键词(权重2)：户外、家庭、办公、节日、商务、聚会
- 属性关键词(权重2)：无添加、氨基酸、健康、营养、品质、精选

【核心判断标准】
1. 商品具备实用性、分享性或纪念性
2. 适合节日、商务、家庭等送礼场景
3. 包装规格符合送礼需求
4. 品牌调性具备体面感

【排除条件】
- 散装、裸装、基础款等低端包装
- 明显个人私用、无赠送场景的商品
- 价格极低且无品牌属性的普通日用品

请基于以上规律进行专业判断。
```

### User Prompt 模板
```
请判断以下商品是否具备礼品属性：

商品信息：
- SKU: {sku}
- 产品名称: {product_name}
- 品牌: {brand}
- 类目: {category_info}
- 价格: {price}
- 商品描述: {description}

请按以下JSON格式输出：
{
    "礼品判断": "是/否/疑似",
    "置信度": "高/中/低", 
    "判断得分": "1-10分",
    "符合特征": ["列出符合的特征"],
    "关键词匹配": ["匹配的关键词"],
    "不符合原因": ["不符合的原因"],
    "建议": "打标建议",
    "详细分析": "判断逻辑说明"
}
```

## 📊 质量控制机制

### 1. 批量处理策略
- **批次大小**: 10-20个商品/批次
- **API调用间隔**: 0.5-1秒
- **失败重试**: 最多3次重试
- **结果验证**: JSON格式校验

### 2. 结果一致性检查
- **温度参数**: 0.1 (确保结果稳定)
- **多次验证**: 对疑似结果进行二次判断
- **人工抽检**: 随机抽取10%结果进行人工验证

### 3. 性能监控
- **准确率监控**: 与人工标注对比
- **处理速度**: 平均每个商品2-3秒
- **成本控制**: Token使用量监控

## 🔄 持续优化

### 1. 规则更新机制
- **月度复盘**: 分析判断结果，调整权重
- **新趋势识别**: 关注新兴礼品类别
- **关键词扩展**: 基于实际数据补充关键词库

### 2. 模型微调
- **Few-shot学习**: 提供典型案例提升准确性
- **领域适应**: 针对特定品类优化提示词
- **反馈循环**: 收集人工纠正结果优化模型

这套判断逻辑结合了规则引擎的精确性和大模型的智能性，能够有效处理新商品的礼品属性判断任务。
